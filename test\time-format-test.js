/**
 * 时间格式化测试文件
 * 用于验证统一时间处理是否正常工作
 */

const TimeUtils = require('../common/TimeUtils.js');

// 测试数据
const testDates = [
  // 标准日期字符串
  '2025-07-11 09:00:00',
  '2025-07-11T09:00:00Z',
  '2025-07-11T09:00:00.000Z',
  
  // 时间戳（毫秒）
  1720684800000,
  
  // 时间戳（秒）
  1720684800,
  
  // Date 对象
  new Date('2025-07-11 09:00:00'),
  
  // 无效数据
  null,
  undefined,
  '',
  'invalid-date',
  'yyyy/MM/dd',
  
  // 边界情况
  '2025-02-29 12:00:00', // 非闰年的2月29日
  '2024-02-29 12:00:00', // 闰年的2月29日
];

console.log('=== 时间格式化测试 ===\n');

testDates.forEach((testDate, index) => {
  console.log(`测试 ${index + 1}: ${JSON.stringify(testDate)}`);
  
  try {
    // 测试解析
    const parsed = TimeUtils.parseDate(testDate);
    console.log(`  解析结果: ${parsed ? parsed.toString() : 'null'}`);
    
    // 测试验证
    const isValid = TimeUtils.isValidDate(testDate);
    console.log(`  是否有效: ${isValid}`);
    
    // 测试格式化
    if (isValid) {
      console.log(`  标准格式: ${TimeUtils.formatTime(testDate)}`);
      console.log(`  完整格式: ${TimeUtils.formatDateTime(testDate)}`);
      console.log(`  日期格式: ${TimeUtils.formatDate(testDate)}`);
      console.log(`  相对时间: ${TimeUtils.formatRelativeTime(testDate)}`);
      console.log(`  本地化格式: ${TimeUtils.formatLocaleTime(testDate)}`);
      console.log(`  是否今天: ${TimeUtils.isToday(testDate)}`);
      console.log(`  是否未来: ${TimeUtils.isFuture(testDate)}`);
    }
  } catch (error) {
    console.log(`  错误: ${error.message}`);
  }
  
  console.log('');
});

// 测试日期差计算
console.log('=== 日期差计算测试 ===\n');
const date1 = '2025-07-15 10:00:00';
const date2 = '2025-07-11 09:00:00';
const daysDiff = TimeUtils.daysDiff(date1, date2);
console.log(`${date1} - ${date2} = ${daysDiff} 天\n`);

// 测试当前时间
console.log('=== 当前时间测试 ===\n');
console.log(`当前时间戳: ${TimeUtils.now()}`);
console.log(`当前时间字符串: ${TimeUtils.nowString()}`);

console.log('\n=== 测试完成 ===');
