import photoWallApi from '../../api/modules/photoWall.js';
import utils from '../utils/util';

Page({
  data: {
    //用户全局信息，page-extend.js会自动注入
    userInfo: null,
    photoDetail: null, // 照片详情
    loading: false, // 是否正在加载
    error: null, // 错误信息
    photoId: null, // 照片ID
  },

  onLoad(options) {
    const { id } = options;
    if (!id) {
      this.setData({
        error: '照片ID不能为空',
      });
      return;
    }

    this.setData({ photoId: id });
    this.loadPhotoDetail();
  },

  /**
   * 加载照片详情
   */
  async loadPhotoDetail() {
    if (this.data.loading) return;

    this.setData({ 
      loading: true,
      error: null,
    });

    try {
      const result = await photoWallApi.getDetail(this.data.photoId);
      
      if (result) {
        this.setData({
          photoDetail: {
            ...result,
            isLiked: false, // 初始化点赞状态，实际应该从后端获取
          },
        });
      } else {
        this.setData({
          error: '照片不存在或已被删除',
        });
      }
    } catch (error) {
      console.error('加载照片详情失败:', error);
      this.setData({
        error: '加载失败，请重试',
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  /**
   * 切换点赞状态
   */
  async toggleLike() {
    if (!this.data.photoDetail) return;

    const { id, isLiked, likeCount } = this.data.photoDetail;
    
    try {
      let result;
      if (isLiked) {
        result = await photoWallApi.unlike(id);
      } else {
        result = await photoWallApi.like(id);
      }

      if (result) {
        this.setData({
          'photoDetail.isLiked': !isLiked,
          'photoDetail.likeCount': result.likeCount || (isLiked ? likeCount - 1 : likeCount + 1),
        });
      }
    } catch (error) {
      console.error('点赞操作失败:', error);
      wx.showToast({
        title: '操作失败',
        icon: 'none',
      });
    }
  },

  /**
   * 预览图片
   */
  previewImage(event) {
    const url = event.currentTarget.dataset.url;
    if (!url) return;

    const { beforePhoto, afterPhoto } = this.data.photoDetail;
    const urls = [beforePhoto, afterPhoto].filter(Boolean);

    wx.previewImage({
      current: url,
      urls: urls,
    });
  },

  /**
   * 返回上一页
   */
  goBack() {
    wx.navigateBack({
      fail: () => {
        // 如果没有上一页，则跳转到照片墙首页
        wx.redirectTo({
          url: '/pages/photoWall/index'
        });
      }
    });
  },

  /**
   * 格式化时间
   */
  formatTime(dateString) {
    const utils = require('../utils/util.js');
    return utils.formatTime(dateString);
  },
});
