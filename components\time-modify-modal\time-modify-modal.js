const TimeUtils = require('../../common/TimeUtils.js');

Component({
  /**
   * 组件的属性列表
   */
  properties: {
    show: {
      type: Boolean,
      value: false
    },
    title: {
      type: String,
      value: '修改服务时间'
    },
    initialTime: {
      type: Object,
      value: {}
    },
    loadingText: {
      type: String,
      value: '修改中...'
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    timeForm: {
      serviceTime: ''
    },
    // 时间选择器相关
    timeArray: [0, 0, 0, 0],
    timeRange: [
      [], // 年份
      [], // 月份
      [], // 日期
      [] // 时间（半小时间隔）
    ],
    showTimePicker: false,
    displayTime: '请选择服务时间' // 用于显示的格式化时间
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 初始化时间选择器
     */
    initTimePicker() {
      const currentDate = new Date();
      const currentYear = currentDate.getFullYear();
      const currentMonth = currentDate.getMonth() + 1;
      const currentDay = currentDate.getDate();

      // 生成年份范围（当前年份到未来2年）
      const years = [];
      for (let year = currentYear; year <= currentYear + 2; year++) {
        years.push(year);
      }

      // 生成月份范围
      const months = Array.from({ length: 12 }, (_, i) => i + 1);

      // 生成日期范围（根据选择的年月）
      const days = Array.from({
        length: this.getDaysInMonth(currentYear, currentMonth)
      }, (_, i) => i + 1);

      // 生成半小时间隔
      const times = [];
      for (let hour = 8; hour < 22; hour++) { // 服务时间8:00-22:00
        times.push(`${hour.toString().padStart(2, '0')}:00`);
        times.push(`${hour.toString().padStart(2, '0')}:30`);
      }

      this.setData({
        timeRange: [years, months, days, times],
        timeArray: [0, currentMonth - 1, currentDay - 1, 0]
      });
    },

    /**
     * 获取月份的天数
     */
    getDaysInMonth(year, month) {
      return new Date(year, month, 0).getDate();
    },

    /**
     * 时间选择器变化
     */
    bindTimeChange(e) {
      const { value } = e.detail;
      this.setData({
        timeArray: value
      });
    },

    /**
     * 处理列变化，用于动态更新日期
     */
    bindTimeColumnChange(e) {
      const { column, value } = e.detail;
      const timeRange = [...this.data.timeRange];
      const timeArray = [...this.data.timeArray];

      if (column === 0 || column === 1) {
        // 更新年份或月份时，重新计算日期
        timeArray[column] = value;
        const selectedYear = timeRange[0][timeArray[0]];
        const selectedMonth = timeRange[1][timeArray[1]];
        const updatedDays = this.getDaysInMonth(selectedYear, selectedMonth);

        timeRange[2] = Array.from({ length: updatedDays }, (_, i) => i + 1);

        // 如果当前选择的日期超出了新月份的天数，调整到最后一天
        if (timeArray[2] >= updatedDays) {
          timeArray[2] = updatedDays - 1;
        }

        this.setData({
          timeRange,
          timeArray
        });
      }
    },

    /**
     * 显示时间选择器
     */
    showTimePickerModal() {
      this.setData({ showTimePicker: true });
    },

    /**
     * 隐藏时间选择器
     */
    hideTimePickerModal() {
      this.setData({ showTimePicker: false });
    },

    /**
     * 确认时间选择
     */
    confirmTimeSelection() {
      const { timeArray, timeRange } = this.data;
      const [yearIndex, monthIndex, dayIndex, timeIndex] = timeArray;

      // 验证索引有效性
      if (yearIndex < 0 || yearIndex >= timeRange[0].length ||
          monthIndex < 0 || monthIndex >= timeRange[1].length ||
          dayIndex < 0 || dayIndex >= timeRange[2].length ||
          timeIndex < 0 || timeIndex >= timeRange[3].length) {
        wx.showToast({
          title: '时间选择错误，请重新选择',
          icon: 'none'
        });
        return;
      }

      const selectedYear = timeRange[0][yearIndex];
      const selectedMonth = timeRange[1][monthIndex];
      const selectedDay = timeRange[2][dayIndex];
      const selectedTime = timeRange[3][timeIndex];

      // 安全地解析时间
      const timeParts = selectedTime.split(':');
      if (timeParts.length !== 2) {
        wx.showToast({
          title: '时间格式错误',
          icon: 'none'
        });
        return;
      }

      const selectedDateTime = new Date(
        selectedYear,
        selectedMonth - 1,
        selectedDay,
        parseInt(timeParts[0], 10),
        parseInt(timeParts[1], 10),
        0
      );

      // 验证生成的日期是否有效
      if (isNaN(selectedDateTime.getTime())) {
        wx.showToast({
          title: '选择的日期无效',
          icon: 'none'
        });
        return;
      }

      const currentDateTime = new Date();

      if (selectedDateTime <= currentDateTime) {
        wx.showToast({
          title: '请选择未来时间',
          icon: 'none'
        });
        return;
      }

      // 使用统一的格式化函数
      const formattedTime = TimeUtils.formatTime(selectedDateTime);
      const displayTime = this.formatDisplayTime(selectedDateTime);

      this.setData({
        'timeForm.serviceTime': formattedTime,
        displayTime: displayTime,
        showTimePicker: false
      });
    },

    /**
     * 关闭弹窗
     */
    close() {
      this.triggerEvent('close');
    },

    /**
     * 确认修改
     */
    confirm() {
      const { timeForm } = this.data;

      if (!timeForm.serviceTime) {
        wx.showToast({
          title: '请选择服务时间',
          icon: 'none'
        });
        return;
      }

      this.triggerEvent('confirm', { timeForm });
    },

    /**
     * 格式化显示时间
     */
    formatDisplayTime(input) {
      if (!input) return '请选择服务时间';

      // 使用统一的日期解析函数
      const date = TimeUtils.parseDate(input);
      if (!date) {
        console.error('Invalid date input:', input);
        return '请选择服务时间';
      }

      // 使用统一的格式化函数
      return TimeUtils.formatTime(date);
    }
  },

  /**
   * 组件生命周期
   */
  lifetimes: {
    attached() {
      this.initTimePicker();
    }
  },

  /**
   * 监听属性变化
   */
  observers: {
    'initialTime': function(newVal) {
      if (newVal && newVal.serviceTime) {
        // 验证初始时间格式
        if (TimeUtils.isValidDate(newVal.serviceTime)) {
          const displayTime = this.formatDisplayTime(newVal.serviceTime);
          this.setData({
            'timeForm.serviceTime': newVal.serviceTime,
            displayTime: displayTime
          });
        } else {
          console.error('Invalid initial time format:', newVal.serviceTime);
          this.setData({
            'timeForm.serviceTime': '',
            displayTime: '请选择服务时间'
          });
        }
      }
    }
  }
});
